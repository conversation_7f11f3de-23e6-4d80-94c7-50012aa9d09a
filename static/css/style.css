/* Custom CSS for Simple Sentiment Analysis Tool */

/* Root Variables */
:root {
    --primary-color: #6f42c1;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-success: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    --gradient-danger: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
    --gradient-info: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    line-height: 1.6;
}

/* Hero Section */
.hero-section {
    background: var(--gradient-primary);
    border-radius: 15px !important;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-section * {
    position: relative;
    z-index: 1;
}

/* Card Enhancements */
.card {
    border-radius: 15px;
    transition: all 0.3s ease;
    border: none;
    box-shadow: var(--shadow-sm);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
    border: none;
    font-weight: 600;
}

/* Button Enhancements */
.btn {
    border-radius: 10px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    text-transform: none;
}

.btn-lg {
    padding: 12px 24px;
    font-size: 1.1rem;
}

.btn-primary {
    background: var(--gradient-primary);
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    background: var(--gradient-primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
    filter: brightness(1.1);
}

.btn-success {
    background: var(--gradient-success);
    box-shadow: var(--shadow-sm);
}

.btn-success:hover {
    background: var(--gradient-success);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
    filter: brightness(1.1);
}

.btn-danger {
    background: var(--gradient-danger);
    box-shadow: var(--shadow-sm);
}

.btn-danger:hover {
    background: var(--gradient-danger);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
    filter: brightness(1.1);
}

.btn-info {
    background: var(--gradient-info);
    box-shadow: var(--shadow-sm);
}

.btn-info:hover {
    background: var(--gradient-info);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
    filter: brightness(1.1);
}

/* Form Enhancements */
.form-control, .form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
    padding: 12px 16px;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(111, 66, 193, 0.25);
    transform: translateY(-1px);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 8px;
}

/* Navigation */
.navbar {
    box-shadow: var(--shadow-sm);
    background: var(--gradient-primary) !important;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

/* Progress Bar */
.progress {
    border-radius: 10px;
    background-color: #e9ecef;
    overflow: hidden;
}

.progress-bar {
    border-radius: 10px;
    transition: width 1s ease-in-out;
}

/* Alert Enhancements */
.alert {
    border-radius: 10px;
    border: none;
    box-shadow: var(--shadow-sm);
}

.alert-success {
    background: linear-gradient(135deg, rgba(25, 135, 84, 0.1) 0%, rgba(56, 239, 125, 0.1) 100%);
    border-left: 4px solid var(--success-color);
}

.alert-danger {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(255, 75, 43, 0.1) 100%);
    border-left: 4px solid var(--danger-color);
}

.alert-info {
    background: linear-gradient(135deg, rgba(13, 202, 240, 0.1) 0%, rgba(102, 126, 234, 0.1) 100%);
    border-left: 4px solid var(--info-color);
}

/* Badge Enhancements */
.badge {
    border-radius: 8px;
    font-weight: 500;
    padding: 6px 12px;
}

/* Table Enhancements */
.table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.table-hover tbody tr:hover {
    background-color: rgba(111, 66, 193, 0.05);
}

/* Chart Container */
.plotly-graph-div {
    border-radius: 10px;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .hero-section {
        padding: 2rem !important;
    }
    
    .hero-section h1 {
        font-size: 2rem !important;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn-lg {
        padding: 10px 20px;
        font-size: 1rem;
    }
    
    .display-4 {
        font-size: 2.5rem;
    }
    
    .display-5 {
        font-size: 2rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
    from { transform: translateY(30px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* Utility Classes */
.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-gradient-primary {
    background: var(--gradient-primary) !important;
}

.bg-gradient-success {
    background: var(--gradient-success) !important;
}

.bg-gradient-danger {
    background: var(--gradient-danger) !important;
}

.bg-gradient-info {
    background: var(--gradient-info) !important;
}

/* Footer */
footer {
    margin-top: auto;
    border-top: 1px solid #e9ecef;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #5a2d91;
}

/* Word Cloud Container */
#wordcloud-container img {
    max-width: 100%;
    height: auto;
    border-radius: 10px;
    box-shadow: var(--shadow-sm);
}

/* Chart Responsiveness */
.js-plotly-plot {
    width: 100% !important;
}

/* File Upload Styling */
.form-control[type="file"] {
    padding: 8px 12px;
}

.form-control[type="file"]::-webkit-file-upload-button {
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 6px 12px;
    margin-right: 10px;
    cursor: pointer;
    font-weight: 500;
}

/* Hover Effects */
.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow);
}
