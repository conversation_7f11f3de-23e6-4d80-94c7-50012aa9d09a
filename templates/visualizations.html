{% extends "base.html" %} {% block title %}Interactive Visualizations - Simple
Sentiment Analysis{% endblock %} {% block content %}
<div class="container-fluid">
  <!-- Header -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center">
        <h1 class="display-5">
          <i class="fas fa-chart-pie me-3"></i>
          Interactive Visualizations
        </h1>
        <div>
          <a
            href="{{ url_for('results') }}"
            class="btn btn-outline-secondary me-2"
          >
            <i class="fas fa-arrow-left me-2"></i>
            Back to Results
          </a>
          <a href="{{ url_for('export_results') }}" class="btn btn-success">
            <i class="fas fa-download me-2"></i>
            Export Data
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- Charts Grid -->
  <div class="row g-4">
    <!-- Sentiment Distribution -->
    {% if charts.sentiment_distribution %}
    <div class="col-lg-6">
      <div class="card border-0 shadow-sm h-100">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0">
            <i class="fas fa-chart-bar me-2"></i>
            Sentiment Distribution by Platform
          </h5>
        </div>
        <div class="card-body">
          <div id="sentiment-distribution-chart"></div>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- Confidence Score Distribution -->
    {% if charts.confidence_distribution %}
    <div class="col-lg-6">
      <div class="card border-0 shadow-sm h-100">
        <div class="card-header bg-info text-white">
          <h5 class="mb-0">
            <i class="fas fa-chart-area me-2"></i>
            Confidence Score Distribution by Platform
          </h5>
        </div>
        <div class="card-body">
          <div id="confidence-distribution-chart"></div>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- Comment Length vs Confidence -->
    {% if charts.sentiment_confidence_scatter %}
    <div class="col-12">
      <div class="card border-0 shadow-sm">
        <div class="card-header bg-secondary text-white">
          <h5 class="mb-0">
            <i class="fas fa-scatter-chart me-2"></i>
            Comment Length vs Confidence Score
          </h5>
        </div>
        <div class="card-body">
          <div id="sentiment-confidence-scatter"></div>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- Time Trends -->
    {% if charts.time_trends %}
    <div class="col-12">
      <div class="card border-0 shadow-sm">
        <div class="card-header bg-warning text-dark">
          <h5 class="mb-0">
            <i class="fas fa-chart-line me-2"></i>
            Sentiment Trends Over Time
          </h5>
        </div>
        <div class="card-body">
          <div id="time-trends-chart"></div>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- Platform Comparison -->
    {% if charts.platform_comparison %}
    <div class="col-lg-6">
      <div class="card border-0 shadow-sm h-100">
        <div class="card-header bg-success text-white">
          <h5 class="mb-0">
            <i class="fas fa-balance-scale me-2"></i>
            Platform and Sentiment Breakdown
          </h5>
        </div>
        <div class="card-body">
          <div id="platform-comparison-chart"></div>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- Word Frequency -->
    {% if charts.word_frequency %}
    <div class="col-lg-6">
      <div class="card border-0 shadow-sm h-100">
        <div class="card-header bg-dark text-white">
          <h5 class="mb-0">
            <i class="fas fa-font me-2"></i>
            Top Words
          </h5>
        </div>
        <div class="card-body">
          <div id="word-frequency-chart"></div>
        </div>
      </div>
    </div>
    {% endif %}
  </div>

  <!-- Comments Data Section -->
  <div class="row mt-4">
    <div class="col-12">
      <div class="card border-0 shadow-sm">
        <div class="card-header bg-gradient bg-info text-white">
          <h5 class="mb-0">
            <i class="fas fa-comments me-2"></i>
            Comments Data
          </h5>
        </div>
        <div class="card-body">
          <!-- Filters -->
          <div class="row mb-3">
            <div class="col-md-4">
              <label for="sentiment-filter" class="form-label"
                >Filter by Sentiment:</label
              >
              <select
                id="sentiment-filter"
                class="form-select"
                onchange="loadComments(1)"
              >
                <option value="all">All Sentiments</option>
                <option value="positive">Positive</option>
                <option value="negative">Negative</option>
                <option value="neutral">Neutral</option>
              </select>
            </div>
            <div class="col-md-4">
              <label for="per-page-select" class="form-label"
                >Comments per page:</label
              >
              <select
                id="per-page-select"
                class="form-select"
                onchange="loadComments(1)"
              >
                <option value="10">10</option>
                <option value="25">25</option>
                <option value="50">50</option>
                <option value="100">100</option>
              </select>
            </div>
            <div class="col-md-4">
              <label for="search-input" class="form-label"
                >Search in comments:</label
              >
              <div class="input-group">
                <input
                  type="text"
                  id="search-input"
                  class="form-control"
                  placeholder="Search comments..."
                />
                <button
                  class="btn btn-outline-secondary"
                  onclick="loadComments(1)"
                >
                  <i class="fas fa-search"></i>
                </button>
              </div>
            </div>
          </div>

          <!-- Comments Container -->
          <div id="comments-container">
            <div class="text-center">
              <button class="btn btn-primary" onclick="loadComments(1)">
                <i class="fas fa-eye me-2"></i>
                Load Comments
              </button>
            </div>
          </div>

          <!-- Pagination -->
          <div
            id="comments-pagination"
            class="d-flex justify-content-center mt-3"
            style="display: none !important"
          ></div>
        </div>
      </div>
    </div>
  </div>

  <!-- Word Cloud Section -->
  <div class="row mt-4">
    <div class="col-12">
      <div class="card border-0 shadow-sm">
        <div class="card-header bg-gradient bg-primary text-white">
          <h5 class="mb-0">
            <i class="fas fa-cloud me-2"></i>
            Word Clouds
          </h5>
        </div>
        <div class="card-body">
          <div class="text-center mb-3">
            <button class="btn btn-primary" onclick="loadWordClouds()">
              <i class="fas fa-sync-alt me-2"></i>
              Generate Word Clouds
            </button>
          </div>
          <div id="wordcloud-container" class="text-center">
            <p class="text-muted">
              Click the button above to generate word clouds
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Export Options -->
  <div class="row mt-4">
    <div class="col-12">
      <div class="card border-0 bg-light">
        <div class="card-header bg-success text-white">
          <h5 class="mb-0">
            <i class="fas fa-download me-2"></i>
            Export Options
          </h5>
        </div>
        <div class="card-body">
          <div class="row text-center">
            <div class="col-md-4">
              <div class="p-3">
                <i class="fas fa-file-csv fa-2x text-success mb-2"></i>
                <h6>Download Analysis</h6>
                <p class="text-muted small">
                  Get the complete dataset with sentiment scores
                </p>
                <a
                  href="{{ url_for('export_results') }}"
                  class="btn btn-success"
                >
                  <i class="fas fa-download me-1"></i>
                  Download CSV
                </a>
              </div>
            </div>
            <div class="col-md-4">
              <div class="p-3">
                <i class="fas fa-chart-bar fa-2x text-info mb-2"></i>
                <h6>Save Charts</h6>
                <p class="text-muted small">
                  Right-click on any chart to save as image
                </p>
                <button class="btn btn-info" onclick="showChartHelp()">
                  <i class="fas fa-question-circle me-1"></i>
                  How to Save
                </button>
              </div>
            </div>
            <div class="col-md-4">
              <div class="p-3">
                <i class="fas fa-share-alt fa-2x text-warning mb-2"></i>
                <h6>Share Results</h6>
                <p class="text-muted small">Copy URL to share this analysis</p>
                <button class="btn btn-warning" onclick="copyURL()">
                  <i class="fas fa-copy me-1"></i>
                  Copy URL
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %} {% block extra_scripts %}
<script>
  // Chart data from Flask
  const charts = {{ charts | tojson }};

  // Debug logging
  console.log('Charts data received:', charts);
  console.log('Plotly version:', typeof Plotly !== 'undefined' ? Plotly.version : 'Plotly not loaded');

  // Render all charts
  document.addEventListener('DOMContentLoaded', function() {
      console.log('DOM loaded, starting chart rendering...');
      renderCharts();
  });

  function renderCharts() {
      let chartsRendered = 0;
      let chartsTotal = 0;

      // Count total charts to render
      Object.keys(charts).forEach(key => {
          if (charts[key] && charts[key].data) {
              chartsTotal++;
          }
      });

      console.log(`Attempting to render ${chartsTotal} charts`);

      // Sentiment Distribution
      if (charts.sentiment_distribution && charts.sentiment_distribution.data) {
          try {
              console.log('Rendering sentiment distribution chart...');
              Plotly.newPlot('sentiment-distribution-chart',
                  charts.sentiment_distribution.data,
                  charts.sentiment_distribution.layout,
                  {responsive: true, displayModeBar: true}
              ).then(() => {
                  console.log('Sentiment distribution chart rendered successfully');
                  chartsRendered++;
                  updateRenderStatus(chartsRendered, chartsTotal);
              }).catch(error => {
                  console.error('Error rendering sentiment distribution chart:', error);
                  showChartError('sentiment-distribution-chart', 'Sentiment Distribution', error);
              });
          } catch (error) {
              console.error('Error setting up sentiment distribution chart:', error);
              showChartError('sentiment-distribution-chart', 'Sentiment Distribution', error);
          }
      } else {
          console.warn('Sentiment distribution chart data not available');
      }

      // Confidence Distribution
      if (charts.confidence_distribution && charts.confidence_distribution.data) {
          try {
              console.log('Rendering confidence distribution chart...');
              Plotly.newPlot('confidence-distribution-chart',
                  charts.confidence_distribution.data,
                  charts.confidence_distribution.layout,
                  {responsive: true, displayModeBar: true}
              ).then(() => {
                  console.log('Confidence distribution chart rendered successfully');
                  chartsRendered++;
                  updateRenderStatus(chartsRendered, chartsTotal);
              }).catch(error => {
                  console.error('Error rendering confidence distribution chart:', error);
                  showChartError('confidence-distribution-chart', 'Confidence Distribution', error);
              });
          } catch (error) {
              console.error('Error setting up confidence distribution chart:', error);
              showChartError('confidence-distribution-chart', 'Confidence Distribution', error);
          }
      } else {
          console.warn('Confidence distribution chart data not available');
      }

      // Sentiment Confidence Scatter
      if (charts.sentiment_confidence_scatter && charts.sentiment_confidence_scatter.data) {
          try {
              console.log('Rendering sentiment confidence scatter chart...');
              Plotly.newPlot('sentiment-confidence-scatter',
                  charts.sentiment_confidence_scatter.data,
                  charts.sentiment_confidence_scatter.layout,
                  {responsive: true, displayModeBar: true}
              ).then(() => {
                  console.log('Sentiment confidence scatter chart rendered successfully');
                  chartsRendered++;
                  updateRenderStatus(chartsRendered, chartsTotal);
              }).catch(error => {
                  console.error('Error rendering sentiment confidence scatter chart:', error);
                  showChartError('sentiment-confidence-scatter', 'Comment Length vs Confidence', error);
              });
          } catch (error) {
              console.error('Error setting up sentiment confidence scatter chart:', error);
              showChartError('sentiment-confidence-scatter', 'Comment Length vs Confidence', error);
          }
      } else {
          console.warn('Sentiment confidence scatter chart data not available');
      }

      // Time Trends
      if (charts.time_trends && charts.time_trends.data) {
          try {
              console.log('Rendering time trends chart...');
              Plotly.newPlot('time-trends-chart',
                  charts.time_trends.data,
                  charts.time_trends.layout,
                  {responsive: true, displayModeBar: true}
              ).then(() => {
                  console.log('Time trends chart rendered successfully');
                  chartsRendered++;
                  updateRenderStatus(chartsRendered, chartsTotal);
              }).catch(error => {
                  console.error('Error rendering time trends chart:', error);
                  showChartError('time-trends-chart', 'Sentiment Trends Over Time', error);
              });
          } catch (error) {
              console.error('Error setting up time trends chart:', error);
              showChartError('time-trends-chart', 'Sentiment Trends Over Time', error);
          }
      } else {
          console.warn('Time trends chart data not available');
      }

      // Platform Comparison
      if (charts.platform_comparison && charts.platform_comparison.data) {
          try {
              console.log('Rendering platform comparison chart...');
              Plotly.newPlot('platform-comparison-chart',
                  charts.platform_comparison.data,
                  charts.platform_comparison.layout,
                  {responsive: true, displayModeBar: true}
              ).then(() => {
                  console.log('Platform comparison chart rendered successfully');
                  chartsRendered++;
                  updateRenderStatus(chartsRendered, chartsTotal);
              }).catch(error => {
                  console.error('Error rendering platform comparison chart:', error);
                  showChartError('platform-comparison-chart', 'Platform Comparison', error);
              });
          } catch (error) {
              console.error('Error setting up platform comparison chart:', error);
              showChartError('platform-comparison-chart', 'Platform Comparison', error);
          }
      } else {
          console.warn('Platform comparison chart data not available');
      }

      // Word Frequency
      if (charts.word_frequency && charts.word_frequency.data) {
          try {
              console.log('Rendering word frequency chart...');
              Plotly.newPlot('word-frequency-chart',
                  charts.word_frequency.data,
                  charts.word_frequency.layout,
                  {responsive: true, displayModeBar: true}
              ).then(() => {
                  console.log('Word frequency chart rendered successfully');
                  chartsRendered++;
                  updateRenderStatus(chartsRendered, chartsTotal);
              }).catch(error => {
                  console.error('Error rendering word frequency chart:', error);
                  showChartError('word-frequency-chart', 'Word Frequency', error);
              });
          } catch (error) {
              console.error('Error setting up word frequency chart:', error);
              showChartError('word-frequency-chart', 'Word Frequency', error);
          }
      } else {
          console.warn('Word frequency chart data not available');
      }

      // If no charts to render, show message
      if (chartsTotal === 0) {
          console.warn('No charts available to render');
          showNoChartsMessage();
      }
  }

  function showChartError(containerId, chartName, error) {
      const container = document.getElementById(containerId);
      if (container) {
          container.innerHTML = `
              <div class="alert alert-warning text-center">
                  <i class="fas fa-exclamation-triangle mb-2"></i>
                  <h6>Unable to load ${chartName}</h6>
                  <p class="small mb-0">Error: ${error.message || error}</p>
                  <button class="btn btn-sm btn-outline-warning mt-2" onclick="location.reload()">
                      <i class="fas fa-refresh me-1"></i>Retry
                  </button>
              </div>
          `;
      }
  }

  function updateRenderStatus(rendered, total) {
      console.log(`Charts rendered: ${rendered}/${total}`);
      if (rendered === total) {
          console.log('All charts rendered successfully!');
      }
  }

  function showNoChartsMessage() {
      const container = document.querySelector('.row.g-4');
      if (container) {
          container.innerHTML = `
              <div class="col-12">
                  <div class="alert alert-info text-center">
                      <i class="fas fa-info-circle fa-2x mb-3"></i>
                      <h5>No visualization data available</h5>
                      <p>The analysis data may not contain sufficient information for generating charts.</p>
                      <a href="{{ url_for('results') }}" class="btn btn-primary">
                          <i class="fas fa-arrow-left me-2"></i>Back to Results
                      </a>
                  </div>
              </div>
          `;
      }
  }

  function loadWordClouds() {
      const container = document.getElementById('wordcloud-container');
      container.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin fa-2x"></i><p>Generating word clouds...</p></div>';

      fetch('/api/wordcloud')
          .then(response => response.json())
          .then(data => {
              console.log('Word cloud data received:', data);

              if (data.error) {
                  container.innerHTML = `<div class="alert alert-warning">${data.error}</div>`;
                  return;
              }

              let html = '<div class="row">';
              let hasAnyWordCloud = false;

              // Overall word cloud
              if (data.overall) {
                  if (data.overall.image) {
                      html += `
                          <div class="col-md-6 mb-3">
                              <h6>Overall Comments</h6>
                              <img src="${data.overall.image}" class="img-fluid rounded" alt="Overall Word Cloud">
                              <p class="small text-muted mt-2">${data.overall.word_count} unique words</p>
                          </div>
                      `;
                      hasAnyWordCloud = true;
                  } else if (data.overall.top_words && data.overall.top_words.length > 0) {
                      // Show top words as fallback
                      html += `
                          <div class="col-md-6 mb-3">
                              <h6>Overall Comments - Top Words</h6>
                              <div class="alert alert-info">
                                  <p class="mb-2">Word cloud image could not be generated, but here are the top words:</p>
                                  <div class="d-flex flex-wrap gap-2">
                                      ${data.overall.top_words.map(([word, freq]) =>
                                          `<span class="badge bg-primary">${word} (${freq})</span>`
                                      ).join('')}
                                  </div>
                                  <p class="small text-muted mt-2">${data.overall.word_count} unique words total</p>
                                  ${data.overall.error ? `<p class="small text-danger">Error: ${data.overall.error}</p>` : ''}
                              </div>
                          </div>
                      `;
                      hasAnyWordCloud = true;
                  }
              }

              // Per-sentiment word clouds
              ['positive', 'negative', 'neutral'].forEach(sentiment => {
                  if (data[sentiment]) {
                      if (data[sentiment].image) {
                          html += `
                              <div class="col-md-6 mb-3">
                                  <h6>${sentiment.charAt(0).toUpperCase() + sentiment.slice(1)} Comments</h6>
                                  <img src="${data[sentiment].image}" class="img-fluid rounded" alt="${sentiment} Word Cloud">
                                  <p class="small text-muted mt-2">${data[sentiment].word_count} unique words</p>
                              </div>
                          `;
                          hasAnyWordCloud = true;
                      } else if (data[sentiment].top_words && data[sentiment].top_words.length > 0) {
                          // Show top words as fallback
                          html += `
                              <div class="col-md-6 mb-3">
                                  <h6>${sentiment.charAt(0).toUpperCase() + sentiment.slice(1)} Comments - Top Words</h6>
                                  <div class="alert alert-info">
                                      <p class="mb-2">Word cloud image could not be generated, but here are the top words:</p>
                                      <div class="d-flex flex-wrap gap-2">
                                          ${data[sentiment].top_words.map(([word, freq]) =>
                                              `<span class="badge bg-secondary">${word} (${freq})</span>`
                                          ).join('')}
                                      </div>
                                      <p class="small text-muted mt-2">${data[sentiment].word_count} unique words total</p>
                                      ${data[sentiment].error ? `<p class="small text-danger">Error: ${data[sentiment].error}</p>` : ''}
                                  </div>
                              </div>
                          `;
                          hasAnyWordCloud = true;
                      }
                  }
              });

              if (!hasAnyWordCloud) {
                  html = `
                      <div class="col-12">
                          <div class="alert alert-warning text-center">
                              <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                              <h5>Word Cloud Generation Failed</h5>
                              <p>Unable to generate word clouds from the current data. This might be due to:</p>
                              <ul class="text-start">
                                  <li>Insufficient text data</li>
                                  <li>System compatibility issues</li>
                                  <li>Text processing errors</li>
                              </ul>
                              <button class="btn btn-outline-warning mt-2" onclick="loadWordClouds()">
                                  <i class="fas fa-retry me-1"></i>Try Again
                              </button>
                          </div>
                      </div>
                  `;
              } else {
                  html += '</div>';
              }

              container.innerHTML = html;
          })
          .catch(error => {
              console.error('Error loading word clouds:', error);
              container.innerHTML = `
                  <div class="alert alert-danger text-center">
                      <i class="fas fa-exclamation-circle fa-2x mb-3"></i>
                      <h5>Error Loading Word Clouds</h5>
                      <p>There was a problem connecting to the word cloud service.</p>
                      <button class="btn btn-outline-danger mt-2" onclick="loadWordClouds()">
                          <i class="fas fa-retry me-1"></i>Try Again
                      </button>
                  </div>
              `;
          });
  }

  function showChartHelp() {
      alert('To save a chart as an image:\n\n1. Hover over the chart\n2. Click the camera icon in the toolbar\n3. The image will be downloaded automatically\n\nYou can also right-click on any chart and select "Save image as..."');
  }

  function copyURL() {
      navigator.clipboard.writeText(window.location.href).then(function() {
          // Show success message
          const btn = event.target.closest('button');
          const originalText = btn.innerHTML;
          btn.innerHTML = '<i class="fas fa-check me-1"></i>Copied!';
          btn.classList.remove('btn-warning');
          btn.classList.add('btn-success');

          setTimeout(() => {
              btn.innerHTML = originalText;
              btn.classList.remove('btn-success');
              btn.classList.add('btn-warning');
          }, 2000);
      });
  }

  // Comments functionality
  let currentCommentsPage = 1;

  function loadComments(page = 1) {
      const container = document.getElementById('comments-container');
      const paginationContainer = document.getElementById('comments-pagination');

      // Show loading
      container.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin fa-2x"></i><p>Loading comments...</p></div>';

      // Get filter values
      const sentiment = document.getElementById('sentiment-filter').value;
      const perPage = document.getElementById('per-page-select').value;
      const search = document.getElementById('search-input').value;

      // Build query parameters
      const params = new URLSearchParams({
          page: page,
          per_page: perPage,
          sentiment: sentiment,
          search: search
      });

      fetch(`/api/comments?${params}`)
          .then(response => response.json())
          .then(data => {
              if (data.error) {
                  container.innerHTML = `<div class="alert alert-warning">${data.error}</div>`;
                  return;
              }

              displayComments(data.comments);
              displayPagination(data.pagination);
              displaySentimentSummary(data.sentiment_counts);
              currentCommentsPage = page;
          })
          .catch(error => {
              console.error('Error loading comments:', error);
              container.innerHTML = '<div class="alert alert-danger">Error loading comments. Please try again.</div>';
          });
  }

  function displayComments(comments) {
      const container = document.getElementById('comments-container');

      if (comments.length === 0) {
          container.innerHTML = '<div class="alert alert-info text-center">No comments found matching your criteria.</div>';
          return;
      }

      let html = '<div class="row">';

      comments.forEach((comment, index) => {
          const sentimentColor = getSentimentColor(comment.sentiment_label);
          const sentimentIcon = getSentimentIcon(comment.sentiment_label);

          html += `
              <div class="col-12 mb-3">
                  <div class="card border-start border-4" style="border-color: ${sentimentColor} !important;">
                      <div class="card-body">
                          <div class="d-flex justify-content-between align-items-start mb-2">
                              <div class="d-flex align-items-center">
                                  <span class="badge" style="background-color: ${sentimentColor};">
                                      <i class="${sentimentIcon} me-1"></i>
                                      ${comment.sentiment_label.charAt(0).toUpperCase() + comment.sentiment_label.slice(1)}
                                  </span>
                                  <small class="text-muted ms-2">
                                      Confidence: ${(comment.sentiment_score * 100).toFixed(1)}%
                                  </small>
                              </div>
                              ${comment.created_at ? `<small class="text-muted">${formatDate(comment.created_at)}</small>` : ''}
                          </div>
                          <p class="card-text">${escapeHtml(comment.comment_text)}</p>
                          ${comment.platform ? `<small class="text-muted"><i class="fas fa-globe me-1"></i>${comment.platform}</small>` : ''}
                      </div>
                  </div>
              </div>
          `;
      });

      html += '</div>';
      container.innerHTML = html;
  }

  function displayPagination(pagination) {
      const container = document.getElementById('comments-pagination');

      if (pagination.total_pages <= 1) {
          container.style.display = 'none';
          return;
      }

      container.style.display = 'flex';

      let html = '<nav><ul class="pagination">';

      // Previous button
      if (pagination.has_prev) {
          html += `<li class="page-item"><a class="page-link" href="#" onclick="loadComments(${pagination.page - 1})">Previous</a></li>`;
      } else {
          html += '<li class="page-item disabled"><span class="page-link">Previous</span></li>';
      }

      // Page numbers
      const startPage = Math.max(1, pagination.page - 2);
      const endPage = Math.min(pagination.total_pages, pagination.page + 2);

      if (startPage > 1) {
          html += '<li class="page-item"><a class="page-link" href="#" onclick="loadComments(1)">1</a></li>';
          if (startPage > 2) {
              html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
          }
      }

      for (let i = startPage; i <= endPage; i++) {
          if (i === pagination.page) {
              html += `<li class="page-item active"><span class="page-link">${i}</span></li>`;
          } else {
              html += `<li class="page-item"><a class="page-link" href="#" onclick="loadComments(${i})">${i}</a></li>`;
          }
      }

      if (endPage < pagination.total_pages) {
          if (endPage < pagination.total_pages - 1) {
              html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
          }
          html += `<li class="page-item"><a class="page-link" href="#" onclick="loadComments(${pagination.total_pages})">${pagination.total_pages}</a></li>`;
      }

      // Next button
      if (pagination.has_next) {
          html += `<li class="page-item"><a class="page-link" href="#" onclick="loadComments(${pagination.page + 1})">Next</a></li>`;
      } else {
          html += '<li class="page-item disabled"><span class="page-link">Next</span></li>';
      }

      html += '</ul></nav>';

      // Add summary
      html += `<div class="ms-3 d-flex align-items-center">
          <small class="text-muted">
              Showing ${((pagination.page - 1) * pagination.per_page) + 1} to ${Math.min(pagination.page * pagination.per_page, pagination.total_items)} of ${pagination.total_items} comments
          </small>
      </div>`;

      container.innerHTML = html;
  }

  function displaySentimentSummary(sentimentCounts) {
      // You can add a summary display here if needed
      console.log('Sentiment distribution:', sentimentCounts);
  }

  function getSentimentColor(sentiment) {
      const colors = {
          'positive': '#2E8B57',
          'negative': '#DC143C',
          'neutral': '#4682B4'
      };
      return colors[sentiment] || '#808080';
  }

  function getSentimentIcon(sentiment) {
      const icons = {
          'positive': 'fas fa-smile',
          'negative': 'fas fa-frown',
          'neutral': 'fas fa-meh'
      };
      return icons[sentiment] || 'fas fa-comment';
  }

  function formatDate(dateString) {
      try {
          const date = new Date(dateString);
          return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
      } catch (e) {
          return dateString;
      }
  }

  function escapeHtml(text) {
      const div = document.createElement('div');
      div.textContent = text;
      return div.innerHTML;
  }

  // Add Enter key support for search
  document.addEventListener('DOMContentLoaded', function() {
      const searchInput = document.getElementById('search-input');
      if (searchInput) {
          searchInput.addEventListener('keypress', function(e) {
              if (e.key === 'Enter') {
                  loadComments(1);
              }
          });
      }
  });
</script>
{% endblock %}
