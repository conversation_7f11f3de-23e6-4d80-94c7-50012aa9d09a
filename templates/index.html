{% extends "base.html" %} {% block title %}Home - Simple Sentiment Analysis{% endblock %} {% block content %}
<div class="container">
  <!-- Hero Section -->
  <div class="row mb-5">
    <div class="col-12 text-center">
      <div class="hero-section bg-primary bg-gradient p-5 rounded-3 text-white">
        <h1 class="display-4 fw-bold mb-3">
          <i class="fas fa-chart-line me-3"></i>
          Simple Sentiment Analysis
        </h1>
        <p class="lead mb-0">Analyze sentiment from text, CSV files, or social media URLs</p>
      </div>
    </div>
  </div>

  <!-- Analysis Options -->
  <div class="row g-4">
    <!-- Single Text Analysis -->
    <div class="col-lg-4">
      <div class="card h-100 shadow-sm border-0">
        <div class="card-header bg-primary text-white text-center">
          <h4 class="mb-0">
            <i class="fas fa-edit me-2"></i>
            Single Text Analysis
          </h4>
        </div>
        <div class="card-body d-flex flex-column">
          <p class="card-text mb-4">Enter any text to get instant sentiment analysis results.</p>

          <form method="POST" action="{{ url_for('analyze_text') }}" class="flex-grow-1 d-flex flex-column">
            <div class="mb-3 flex-grow-1">
              <label for="text" class="form-label">Enter text:</label>
              <textarea class="form-control" id="text" name="text" rows="6" placeholder="Type or paste your text here..." required></textarea>
            </div>
            <button type="submit" class="btn btn-primary btn-lg w-100">
              <i class="fas fa-search me-2"></i>
              Analyze Sentiment
            </button>
          </form>
        </div>
      </div>
    </div>

    <!-- CSV Upload Analysis -->
    <div class="col-lg-4">
      <div class="card h-100 shadow-sm border-0">
        <div class="card-header bg-success text-white text-center">
          <h4 class="mb-0">
            <i class="fas fa-file-csv me-2"></i>
            CSV Upload Analysis
          </h4>
        </div>
        <div class="card-body d-flex flex-column">
          <p class="card-text mb-4">Upload a CSV file with a 'comment_text' column for batch analysis.</p>

          <form method="POST" action="{{ url_for('analyze_csv') }}" enctype="multipart/form-data" class="flex-grow-1 d-flex flex-column">
            <div class="mb-3">
              <label for="file" class="form-label">Upload CSV with 'comment_text' column:</label>
              <input class="form-control" type="file" id="file" name="file" accept=".csv" required />
              <div class="form-text">
                <small>File must contain a column named 'comment_text'</small>
              </div>
            </div>

            <!-- Tombol Download Template -->
            <div class="mb-3">
              <div class="d-grid">
                <a href="{{ url_for('download_template') }}" class="btn btn-outline-success btn-sm">
                  <i class="fas fa-download me-2"></i>
                  Download Template CSV
                </a>
              </div>
              <div class="form-text mt-1">
                <small class="text-muted">Download contoh format CSV yang benar</small>
              </div>
            </div>

            <div class="mt-auto">
              <button type="submit" class="btn btn-success btn-lg w-100">
                <i class="fas fa-upload me-2"></i>
                Upload & Analyze
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- URL Scraping Analysis -->
    <div class="col-lg-4">
      <div class="card h-100 shadow-sm border-0">
        <div class="card-header bg-info text-white text-center">
          <h4 class="mb-0">
            <i class="fas fa-link me-2"></i>
            URL Scraping Analysis
          </h4>
        </div>
        <div class="card-body d-flex flex-column">
          <p class="card-text mb-4">Enter Instagram or TikTok URL to scrape and analyze comments.</p>

          <form method="POST" action="{{ url_for('analyze_url') }}" class="flex-grow-1 d-flex flex-column">
            <div class="mb-3">
              <label for="platform" class="form-label">Platform:</label>
              <select class="form-select" id="platform" name="platform" required>
                <option value="">Select Platform</option>
                <option value="instagram">Instagram</option>
                <option value="tiktok">TikTok</option>
              </select>
            </div>

            <div class="mb-3 flex-grow-1">
              <label for="url" class="form-label">Post URL:</label>
              <input type="url" class="form-control" id="url" name="url" placeholder="https://www.instagram.com/p/ABC123/ or https://www.tiktok.com/@user/video/123456789" required />
            </div>

            <div class="mt-auto">
              <button type="submit" class="btn btn-info btn-lg w-100">
                <i class="fas fa-download me-2"></i>
                Scrape & Analyze
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <!-- How to Use Section -->
  <div class="row mt-5">
    <div class="col-12">
      <div class="card border-0 bg-light">
        <div class="card-header bg-dark text-white">
          <h4 class="mb-0">
            <i class="fas fa-question-circle me-2"></i>
            How to Use
          </h4>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-4">
              <h5 class="text-primary">
                <i class="fas fa-edit me-2"></i>
                Single Text
              </h5>
              <p>Enter any text to get sentiment analysis.</p>
            </div>
            <div class="col-md-4">
              <h5 class="text-success">
                <i class="fas fa-file-csv me-2"></i>
                CSV Upload
              </h5>
              <p>Upload CSV with 'comment_text' column for batch analysis.</p>
            </div>
            <div class="col-md-4">
              <h5 class="text-info">
                <i class="fas fa-link me-2"></i>
                URL Scraping
              </h5>
              <p>Enter Instagram or TikTok URL to scrape and analyze comments.</p>
            </div>
          </div>

          <div class="mt-4 p-3 bg-primary bg-opacity-10 rounded">
            <h6 class="text-primary mb-2">
              <i class="fas fa-lightbulb me-2"></i>
              URL Examples:
            </h6>
            <div class="row">
              <div class="col-md-6">
                <strong>Instagram:</strong>
                <code>https://www.instagram.com/p/DNcgnWvRJ9-/</code>
              </div>
              <div class="col-md-6">
                <strong>TikTok:</strong>
                <code>https://www.tiktok.com/@putra11__/video/7539605848159489286</code>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %} {% block extra_scripts %}
<script>
  // Add some interactivity
  document.addEventListener('DOMContentLoaded', function () {
    // Auto-detect platform from URL
    const urlInput = document.getElementById('url');
    const platformSelect = document.getElementById('platform');

    if (urlInput && platformSelect) {
      urlInput.addEventListener('input', function () {
        const url = this.value.toLowerCase();
        if (url.includes('instagram.com')) {
          platformSelect.value = 'instagram';
        } else if (url.includes('tiktok.com')) {
          platformSelect.value = 'tiktok';
        }
      });
    }

    // Form validation feedback
    const forms = document.querySelectorAll('form');
    forms.forEach((form) => {
      form.addEventListener('submit', function (e) {
        const submitBtn = this.querySelector('button[type="submit"]');
        if (submitBtn) {
          submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
          submitBtn.disabled = true;
        }
      });
    });
  });
</script>
{% endblock %}
