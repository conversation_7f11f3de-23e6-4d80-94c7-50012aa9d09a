"""
Word Cloud Generator for Sentiment Analysis
"""

import pandas as pd
import numpy as np
from wordcloud import WordCloud
import base64
from io import BytesIO
import re
from collections import Counter
import os
import sys

# Force matplotlib to use non-GUI backend before importing pyplot
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt

# Disable matplotlib GUI warnings
import warnings
warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')

def generate_wordcloud_data(df):
    """
    Generate word cloud data for overall and per-sentiment analysis

    Args:
        df (pandas.DataFrame): DataFrame with sentiment analysis results

    Returns:
        dict: Dictionary containing word cloud data
    """
    try:
        wordcloud_data = {}

        # Check if we have valid data
        if df.empty or 'comment_text' not in df.columns:
            return {
                'error': 'No valid data available for word cloud generation',
                'overall': {'image': None, 'word_count': 0, 'top_words': []},
            }

        # Overall word cloud
        print("Generating overall word cloud...")
        wordcloud_data['overall'] = create_wordcloud_image(df, 'Overall Comments')

        # Per-sentiment word clouds
        sentiments = df['sentiment_label'].unique()
        print(f"Generating word clouds for sentiments: {list(sentiments)}")

        for sentiment in sentiments:
            try:
                sentiment_df = df[df['sentiment_label'] == sentiment]
                if not sentiment_df.empty:
                    wordcloud_data[sentiment] = create_wordcloud_image(
                        sentiment_df,
                        f'{sentiment.title()} Comments'
                    )
                else:
                    wordcloud_data[sentiment] = {
                        'image': None,
                        'word_count': 0,
                        'top_words': [],
                        'error': f'No {sentiment} comments found'
                    }
            except Exception as e:
                print(f"Error generating word cloud for {sentiment}: {e}")
                wordcloud_data[sentiment] = {
                    'image': None,
                    'word_count': 0,
                    'top_words': [],
                    'error': str(e)
                }

        return wordcloud_data

    except Exception as e:
        print(f"Error in generate_wordcloud_data: {e}")
        return {
            'error': f'Word cloud generation failed: {str(e)}',
            'overall': {'image': None, 'word_count': 0, 'top_words': []},
        }

def create_wordcloud_image(df, title):
    """
    Create word cloud image from DataFrame

    Args:
        df (pandas.DataFrame): DataFrame containing comment text
        title (str): Title for the word cloud

    Returns:
        dict: Dictionary with image data and statistics
    """
    if df.empty:
        return {
            'image': None,
            'word_count': 0,
            'top_words': [],
            'error': 'No data available'
        }

    try:
        # Combine all text
        text = ' '.join(df['comment_text'].fillna('').astype(str))

        # Clean and process text
        cleaned_text = clean_text_for_wordcloud(text)

        if not cleaned_text.strip():
            return {
                'image': None,
                'word_count': 0,
                'top_words': [],
                'error': 'No valid text found after cleaning'
            }

        # Create word cloud with thread-safe approach
        return _create_wordcloud_safe(cleaned_text, title)

    except Exception as e:
        print(f"Error in create_wordcloud_image: {e}")
        return {
            'image': None,
            'word_count': 0,
            'top_words': [],
            'error': str(e)
        }

def _create_wordcloud_safe(cleaned_text, title):
    """
    Thread-safe word cloud creation without matplotlib
    """
    try:
        # Create word cloud
        wordcloud = WordCloud(
            width=800,
            height=400,
            background_color='white',
            max_words=100,
            colormap='viridis',
            relative_scaling=0.5,
            min_font_size=10,
            prefer_horizontal=0.9
        ).generate(cleaned_text)

        # Get word frequencies first
        word_freq = wordcloud.words_
        top_words = list(word_freq.items())[:10]

        # Try to create image using PIL instead of matplotlib
        try:
            # Convert wordcloud to PIL Image
            wordcloud_image = wordcloud.to_image()

            # Save to buffer
            img_buffer = BytesIO()
            wordcloud_image.save(img_buffer, format='PNG')
            img_buffer.seek(0)

            # Convert to base64
            img_base64 = base64.b64encode(img_buffer.getvalue()).decode()
            img_buffer.close()

            return {
                'image': f'data:image/png;base64,{img_base64}',
                'word_count': len(word_freq),
                'top_words': top_words
            }

        except Exception as img_error:
            print(f"Error creating word cloud image with PIL: {img_error}")
            # Return word frequency data without image
            return {
                'image': None,
                'word_count': len(word_freq),
                'top_words': top_words,
                'error': f'Image generation failed: {str(img_error)}'
            }

    except Exception as e:
        print(f"Error creating word cloud: {e}")
        # Return word frequency data even if wordcloud creation fails
        try:
            words = cleaned_text.split()
            word_freq = Counter(words)
            top_words = word_freq.most_common(10)

            return {
                'image': None,
                'word_count': len(word_freq),
                'top_words': top_words,
                'error': f'WordCloud generation failed: {str(e)}'
            }
        except Exception as fallback_error:
            return {
                'image': None,
                'word_count': 0,
                'top_words': [],
                'error': f'Complete failure: {str(fallback_error)}'
            }

def clean_text_for_wordcloud(text):
    """
    Clean text for word cloud generation
    
    Args:
        text (str): Raw text to clean
        
    Returns:
        str: Cleaned text
    """
    if not isinstance(text, str):
        return ""
    
    # Convert to lowercase
    text = text.lower()
    
    # Remove URLs
    text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
    
    # Remove mentions and hashtags (optional - you might want to keep hashtags)
    text = re.sub(r'@\w+', '', text)
    # text = re.sub(r'#\w+', '', text)  # Uncomment to remove hashtags
    
    # Remove special characters but keep letters, numbers, and spaces
    text = re.sub(r'[^a-zA-Z0-9\s#]', ' ', text)
    
    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text)
    
    # Remove common stop words
    stop_words = {
        'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 
        'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 
        'how', 'man', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy', 
        'did', 'its', 'let', 'put', 'say', 'she', 'too', 'use', 'this', 'that',
        'with', 'have', 'will', 'your', 'from', 'they', 'know', 'want', 'been',
        'good', 'much', 'some', 'time', 'very', 'when', 'come', 'here', 'just',
        'like', 'long', 'make', 'many', 'over', 'such', 'take', 'than', 'them',
        'well', 'were', 'what', 'would', 'there', 'could', 'other', 'after',
        'first', 'never', 'these', 'think', 'where', 'being', 'every', 'great',
        'might', 'shall', 'still', 'those', 'under', 'while', 'about', 'before',
        'should', 'through', 'during', 'follow', 'around', 'really', 'something'
    }
    
    # Filter out stop words
    words = text.split()
    filtered_words = [word for word in words if word not in stop_words and len(word) > 2]
    
    return ' '.join(filtered_words)

def get_word_frequencies(df, sentiment=None):
    """
    Get word frequencies for a DataFrame
    
    Args:
        df (pandas.DataFrame): DataFrame with comment text
        sentiment (str, optional): Filter by sentiment
        
    Returns:
        list: List of (word, frequency) tuples
    """
    if sentiment:
        df = df[df['sentiment_label'] == sentiment]
    
    if df.empty:
        return []
    
    # Combine all text
    text = ' '.join(df['comment_text'].fillna('').astype(str))
    
    # Clean text
    cleaned_text = clean_text_for_wordcloud(text)
    
    # Get word frequencies
    words = cleaned_text.split()
    word_freq = Counter(words)
    
    return word_freq.most_common(50)

def create_simple_wordcloud_data(df):
    """
    Create simple word cloud data without images (for API responses)
    
    Args:
        df (pandas.DataFrame): DataFrame with sentiment analysis results
        
    Returns:
        dict: Dictionary containing word frequency data
    """
    wordcloud_data = {}
    
    # Overall word frequencies
    wordcloud_data['overall'] = get_word_frequencies(df)
    
    # Per-sentiment word frequencies
    for sentiment in df['sentiment_label'].unique():
        wordcloud_data[sentiment] = get_word_frequencies(df, sentiment)
    
    return wordcloud_data
