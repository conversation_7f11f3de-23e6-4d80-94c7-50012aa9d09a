"""
Visualization module for sentiment analysis results
Creates interactive Plotly charts
"""

import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import json
from datetime import datetime
import numpy as np

def create_visualizations(df, analysis_type='csv_upload'):
    """
    Create all visualizations for sentiment analysis results

    Args:
        df (pandas.DataFrame): DataFrame with sentiment analysis results
        analysis_type (str): Type of analysis performed

    Returns:
        dict: Dictionary containing all chart JSON data
    """
    charts = {}

    if df.empty:
        print("Warning: DataFrame is empty, no charts will be generated")
        return charts

    print(f"Creating visualizations for {len(df)} records")
    print(f"DataFrame columns: {list(df.columns)}")
    print(f"Analysis type: {analysis_type}")

    try:
        # 1. Sentiment Distribution Chart
        print("Creating sentiment distribution chart...")
        charts['sentiment_distribution'] = create_sentiment_distribution_chart(df)
        print("✓ Sentiment distribution chart created")
    except Exception as e:
        print(f"Error creating sentiment distribution chart: {e}")
        charts['sentiment_distribution'] = create_empty_chart("Sentiment Distribution", str(e))

    try:
        # 2. Confidence Score Distribution
        print("Creating confidence distribution chart...")
        charts['confidence_distribution'] = create_confidence_distribution_chart(df)
        print("✓ Confidence distribution chart created")
    except Exception as e:
        print(f"Error creating confidence distribution chart: {e}")
        charts['confidence_distribution'] = create_empty_chart("Confidence Distribution", str(e))

    try:
        # 3. Sentiment vs Confidence Scatter Plot
        print("Creating sentiment confidence scatter chart...")
        charts['sentiment_confidence_scatter'] = create_sentiment_confidence_scatter(df)
        print("✓ Sentiment confidence scatter chart created")
    except Exception as e:
        print(f"Error creating sentiment confidence scatter chart: {e}")
        charts['sentiment_confidence_scatter'] = create_empty_chart("Comment Length vs Confidence", str(e))

    try:
        # 4. Platform Comparison (if platform data exists)
        if 'platform' in df.columns or analysis_type == 'url_scraping':
            print("Creating platform comparison chart...")
            charts['platform_comparison'] = create_platform_comparison_chart(df)
            print("✓ Platform comparison chart created")
    except Exception as e:
        print(f"Error creating platform comparison chart: {e}")
        charts['platform_comparison'] = create_empty_chart("Platform Comparison", str(e))

    try:
        # 5. Time-based analysis (if timestamp data exists)
        if 'created_at' in df.columns:
            print("Creating time trends chart...")
            charts['time_trends'] = create_time_trends_chart(df)
            print("✓ Time trends chart created")
    except Exception as e:
        print(f"Error creating time trends chart: {e}")
        charts['time_trends'] = create_empty_chart("Time Trends", str(e))

    try:
        # 6. Top Words by Sentiment (basic version)
        print("Creating word frequency chart...")
        charts['word_frequency'] = create_word_frequency_chart(df)
        print("✓ Word frequency chart created")
    except Exception as e:
        print(f"Error creating word frequency chart: {e}")
        charts['word_frequency'] = create_empty_chart("Word Frequency", str(e))

    print(f"Charts created: {list(charts.keys())}")
    return charts

def create_empty_chart(title, error_message):
    """Create an empty chart with error message"""
    fig = go.Figure()
    fig.add_annotation(
        text=f"Unable to generate {title}<br>Error: {error_message}",
        xref="paper", yref="paper",
        x=0.5, y=0.5, xanchor='center', yanchor='middle',
        showarrow=False,
        font=dict(size=14, color="red")
    )
    fig.update_layout(
        title=title,
        template='plotly_white',
        height=400,
        xaxis=dict(visible=False),
        yaxis=dict(visible=False)
    )
    return json.loads(fig.to_json())

def create_sentiment_distribution_chart(df):
    """Create sentiment distribution bar chart"""
    if 'sentiment_label' not in df.columns:
        raise ValueError("Column 'sentiment_label' not found in DataFrame")

    sentiment_counts = df['sentiment_label'].value_counts()

    if sentiment_counts.empty:
        raise ValueError("No sentiment data available")

    # Define colors for sentiments
    colors = {
        'positive': '#2E8B57',  # Sea Green
        'negative': '#DC143C',  # Crimson
        'neutral': '#4682B4'    # Steel Blue
    }

    # Ensure we have valid data
    x_values = sentiment_counts.index.tolist()
    y_values = sentiment_counts.values.tolist()

    fig = go.Figure(data=[
        go.Bar(
            x=x_values,
            y=y_values,
            marker_color=[colors.get(sentiment, '#808080') for sentiment in x_values],
            text=y_values,
            textposition='auto',
            textfont=dict(color='white', size=12)
        )
    ])

    fig.update_layout(
        title='Sentiment Distribution',
        xaxis_title='Sentiment',
        yaxis_title='Number of Comments',
        template='plotly_white',
        height=400,
        showlegend=False
    )

    return json.loads(fig.to_json())

def create_confidence_distribution_chart(df):
    """Create confidence score distribution box plot"""
    if 'sentiment_label' not in df.columns:
        raise ValueError("Column 'sentiment_label' not found in DataFrame")
    if 'sentiment_score' not in df.columns:
        raise ValueError("Column 'sentiment_score' not found in DataFrame")

    fig = go.Figure()

    colors = {
        'positive': '#2E8B57',
        'negative': '#DC143C',
        'neutral': '#4682B4'
    }

    sentiments = df['sentiment_label'].unique()
    if len(sentiments) == 0:
        raise ValueError("No sentiment data available")

    for sentiment in sentiments:
        sentiment_data = df[df['sentiment_label'] == sentiment]['sentiment_score']

        if len(sentiment_data) > 0:
            fig.add_trace(go.Box(
                y=sentiment_data.tolist(),
                name=sentiment.title(),
                marker_color=colors.get(sentiment, '#808080'),
                boxpoints='outliers'
            ))

    fig.update_layout(
        title='Confidence Score Distribution by Sentiment',
        xaxis_title='Sentiment',
        yaxis_title='Confidence Score',
        template='plotly_white',
        height=400
    )

    return json.loads(fig.to_json())

def create_sentiment_confidence_scatter(df):
    """Create scatter plot of comment length vs confidence score"""
    if 'sentiment_label' not in df.columns:
        raise ValueError("Column 'sentiment_label' not found in DataFrame")
    if 'sentiment_score' not in df.columns:
        raise ValueError("Column 'sentiment_score' not found in DataFrame")
    if 'comment_text' not in df.columns:
        raise ValueError("Column 'comment_text' not found in DataFrame")

    # Calculate comment length
    df_copy = df.copy()
    df_copy['comment_length'] = df_copy['comment_text'].astype(str).str.len()

    colors = {
        'positive': '#2E8B57',
        'negative': '#DC143C',
        'neutral': '#4682B4'
    }

    fig = go.Figure()

    sentiments = df_copy['sentiment_label'].unique()
    if len(sentiments) == 0:
        raise ValueError("No sentiment data available")

    for sentiment in sentiments:
        sentiment_data = df_copy[df_copy['sentiment_label'] == sentiment]

        if len(sentiment_data) > 0:
            # Ensure we have valid numeric data
            x_values = sentiment_data['comment_length'].tolist()
            y_values = sentiment_data['sentiment_score'].tolist()
            text_values = [str(text)[:100] + '...' if len(str(text)) > 100 else str(text)
                          for text in sentiment_data['comment_text'].tolist()]

            fig.add_trace(go.Scatter(
                x=x_values,
                y=y_values,
                mode='markers',
                name=sentiment.title(),
                marker=dict(
                    color=colors.get(sentiment, '#808080'),
                    size=8,
                    opacity=0.6
                ),
                text=text_values,
                hovertemplate='<b>%{text}</b><br>Length: %{x}<br>Confidence: %{y:.3f}<extra></extra>'
            ))

    fig.update_layout(
        title='Comment Length vs Confidence Score',
        xaxis_title='Comment Length (characters)',
        yaxis_title='Confidence Score',
        template='plotly_white',
        height=400
    )

    return json.loads(fig.to_json())

def create_platform_comparison_chart(df):
    """Create platform comparison chart if platform data exists"""
    if 'platform' not in df.columns:
        # If no platform column, create one based on post_id patterns
        df_copy = df.copy()
        df_copy['platform'] = 'Unknown'
        return json.loads(go.Figure().to_json())
    
    platform_sentiment = df.groupby(['platform', 'sentiment_label']).size().unstack(fill_value=0)
    
    fig = go.Figure()
    
    colors = {
        'positive': '#2E8B57',
        'negative': '#DC143C',
        'neutral': '#4682B4'
    }
    
    for sentiment in platform_sentiment.columns:
        fig.add_trace(go.Bar(
            name=sentiment.title(),
            x=platform_sentiment.index,
            y=platform_sentiment[sentiment],
            marker_color=colors.get(sentiment, '#808080')
        ))
    
    fig.update_layout(
        title='Sentiment Distribution by Platform',
        xaxis_title='Platform',
        yaxis_title='Number of Comments',
        barmode='group',
        template='plotly_white',
        height=400
    )
    
    return json.loads(fig.to_json())

def create_time_trends_chart(df):
    """Create time-based sentiment trends"""
    if 'created_at' not in df.columns:
        return json.loads(go.Figure().to_json())
    
    df_copy = df.copy()
    
    # Convert timestamps to datetime
    try:
        # Handle different timestamp formats
        df_copy['datetime'] = pd.to_datetime(df_copy['created_at'], unit='s', errors='coerce')
        if df_copy['datetime'].isna().all():
            df_copy['datetime'] = pd.to_datetime(df_copy['created_at'], errors='coerce')
    except:
        return json.loads(go.Figure().to_json())
    
    # Remove rows with invalid dates
    df_copy = df_copy.dropna(subset=['datetime'])
    
    if df_copy.empty:
        return json.loads(go.Figure().to_json())
    
    # Group by hour and sentiment
    df_copy['hour'] = df_copy['datetime'].dt.floor('H')
    hourly_sentiment = df_copy.groupby(['hour', 'sentiment_label']).size().unstack(fill_value=0)
    
    fig = go.Figure()
    
    colors = {
        'positive': '#2E8B57',
        'negative': '#DC143C',
        'neutral': '#4682B4'
    }
    
    for sentiment in hourly_sentiment.columns:
        fig.add_trace(go.Scatter(
            x=hourly_sentiment.index,
            y=hourly_sentiment[sentiment],
            mode='lines+markers',
            name=sentiment.title(),
            line=dict(color=colors.get(sentiment, '#808080'))
        ))
    
    fig.update_layout(
        title='Sentiment Trends Over Time',
        xaxis_title='Time',
        yaxis_title='Number of Comments',
        template='plotly_white',
        height=400
    )
    
    return json.loads(fig.to_json())

def create_word_frequency_chart(df):
    """Create simple word frequency chart"""
    from collections import Counter
    import re
    
    # Combine all comments
    all_text = ' '.join(df['comment_text'].fillna('').astype(str))
    
    # Simple word extraction (basic tokenization)
    words = re.findall(r'\b[a-zA-Z]{3,}\b', all_text.lower())
    
    # Remove common stop words
    stop_words = {'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'man', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy', 'did', 'its', 'let', 'put', 'say', 'she', 'too', 'use'}
    words = [word for word in words if word not in stop_words]
    
    # Get top 20 words
    word_counts = Counter(words).most_common(20)
    
    if not word_counts:
        return json.loads(go.Figure().to_json())
    
    words, counts = zip(*word_counts)
    
    fig = go.Figure(data=[
        go.Bar(
            x=list(counts),
            y=list(words),
            orientation='h',
            marker_color='#4682B4'
        )
    ])
    
    fig.update_layout(
        title='Top 20 Most Frequent Words',
        xaxis_title='Frequency',
        yaxis_title='Words',
        template='plotly_white',
        height=500,
        yaxis={'categoryorder': 'total ascending'}
    )
    
    return json.loads(fig.to_json())
